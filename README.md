# Altitude Preselector Arduino Controller

This Arduino sketch controls a Sperry altitude preselector instrument by reading voltage spikes from a DC motor encoder and grounding specific output pins to display altitude values. The altitude counter starts at 000000 and increments/decrements by 100 based on motor rotation direction.

## Hardware Requirements

- Arduino Uno or compatible microcontroller
- DC motor encoder with 2-channel output (connected to analog pins A0 and A1)
- 6 output connections to the instrument (DD0-DD5, pins 3-8)
- Pull-up resistors on output pins (if required by instrument)

## Pin Connections

| Arduino Pin | Function | Description |
|-------------|----------|-------------|
| A0 | Encoder Channel A | Analog input for encoder channel A |
| A1 | Encoder Channel B | Analog input for encoder channel B |
| 3 | DD0 Output | Digital control output 0 |
| 4 | DD1 Output | Digital control output 1 |
| 5 | DD2 Output | Digital control output 2 |
| 6 | DD3 Output | Digital control output 3 |
| 7 | DD4 Output | Digital control output 4 (10,000's) |
| 8 | DD5 Output | Digital control output 5 (1,000's) |

## How It Works

1. **Encoder Input**: The Arduino monitors voltage spikes on analog pins A0 and A1
2. **Direction Detection**: Determines rotation direction based on which channel triggers first
3. **Altitude Counter**: Increments or decrements altitude by 100 based on direction
4. **Display Control**: Grounds specific output pins to display the current altitude value

## Configuration

### Voltage Threshold
Adjust the `VOLTAGE_THRESHOLD` constant to match your encoder's output voltage:

```cpp
const int VOLTAGE_THRESHOLD = 512;   // Threshold for detecting voltage spike (adjust as needed)
```

For a 5V system, 512 represents about 2.5V. Adjust this value based on your encoder's output characteristics.

### Debounce Delay
The `DEBOUNCE_DELAY` prevents multiple triggers from a single encoder pulse:

```cpp
const int DEBOUNCE_DELAY = 50;       // Debounce delay in milliseconds
```

### Altitude Range and Increment
The altitude counter operates from 0 to 999900 in increments of 100:

```cpp
const long MAX_ALTITUDE = 999900;    // Maximum altitude (999900 in increments of 100)
const int INCREMENT = 100;           // Altitude increment per encoder step
```

## Usage

1. Upload the sketch to your Arduino
2. Connect the encoder channels A and B to analog pins A0 and A1
3. Connect the output pins (3-8) to the instrument's DD0-DD5 inputs
4. Open the Serial Monitor (9600 baud) to view status information
5. Turn the encoder/motor to increment or decrement the altitude display

## Encoder Direction Logic

The system determines direction based on which channel triggers first:
- **Channel A triggers first**: If Channel B is also high → Count UP, if Channel B is low → Count DOWN
- **Channel B triggers first**: If Channel A is also high → Count UP, if Channel A is low → Count DOWN

## Testing

### Manual Testing Functions

The sketch includes testing functions that can be called from code or modified for button input:

- `setManualAltitude(altitude)` - Set a specific altitude for testing
- `testAltitudeRange()` - Cycle through altitude values 0-1000 in increments of 100

### Serial Monitor Output

The Serial Monitor displays:
- Current altitude value
- Real-time encoder channel A and B voltage readings
- Status messages when altitude changes

## Troubleshooting

1. **No Encoder Response**: Check encoder connections and verify voltage levels reach the threshold
2. **Wrong Direction**: Swap encoder channel A and B connections
3. **Multiple Counts**: Increase the `DEBOUNCE_DELAY` value
4. **Erratic Behavior**: Lower the `VOLTAGE_THRESHOLD` or add filtering capacitors to encoder inputs

## Based On

Sperry Component Maintenance Manual PN 7004577, Test Description for Test 7.16

## Notes

- The instrument expects pins to be grounded (LOW) to activate specific display segments
- Pins are set HIGH (not grounded) by default
- The current implementation uses a simple binary representation for display control
- You may need to modify the `setAltitudeDisplay()` function to match your specific instrument's requirements
- The altitude counter starts at 000000 and increments/decrements by 100
- Maximum altitude is 999900 (6 digits with 100-unit increments)
