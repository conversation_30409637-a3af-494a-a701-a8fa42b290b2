# Altitude Preselector Arduino Controller

This Arduino sketch controls a Sperry altitude preselector instrument by reading a tachometer input and grounding specific output pins to display altitude values.

## Hardware Requirements

- Arduino Uno or compatible microcontroller
- Tachometer signal input (connected to pin 2)
- 6 output connections to the instrument (DD0-DD5, pins 3-8)
- Pull-up resistors on output pins (if required by instrument)

## Pin Connections

| Arduino Pin | Function | Description |
|-------------|----------|-------------|
| 2 | Tachometer Input | RPM signal input (interrupt capable) |
| 3 | DD0 Output | Digital control output 0 |
| 4 | DD1 Output | Digital control output 1 |
| 5 | DD2 Output | Digital control output 2 |
| 6 | DD3 Output | Digital control output 3 |
| 7 | DD4 Output | Digital control output 4 (10,000's) |
| 8 | DD5 Output | Digital control output 5 (1,000's) |

## How It Works

1. **Tachometer Input**: The Arduino reads RPM pulses on pin 2 using an interrupt
2. **RPM Calculation**: Calculates current RPM based on pulse frequency
3. **Altitude Mapping**: Converts RPM to target altitude using a linear mapping
4. **Display Control**: Grounds specific output pins according to the lookup table to display the altitude

## Configuration

### RPM to Altitude Mapping
The default mapping converts 0-3000 RPM to 0-30000 feet altitude. Modify the `updateTargetAltitude()` function to change this mapping:

```cpp
targetAltitude = map(currentRPM, 0, 3000, MIN_ALTITUDE, 30000);
```

### Altitude Lookup Table
The `altitudeCodes[]` array contains the pin patterns for each altitude. Based on the Sperry manual, each altitude corresponds to a specific combination of grounded pins.

### Tachometer Pulses Per Revolution
If your tachometer generates multiple pulses per revolution, adjust the RPM calculation in `updateRPM()`:

```cpp
// For 2 pulses per revolution:
currentRPM = (tachPulseCount * 60000) / (RPM_UPDATE_INTERVAL * 2);
```

## Usage

1. Upload the sketch to your Arduino
2. Connect the tachometer signal to pin 2
3. Connect the output pins (3-8) to the instrument's DD0-DD5 inputs
4. Open the Serial Monitor (9600 baud) to view status information
5. The display will automatically update based on the tachometer input

## Testing

### Manual Testing Functions

The sketch includes testing functions that can be called from the Serial Monitor or modified for button input:

- `setManualAltitude(altitude)` - Set a specific altitude for testing
- `testAllAltitudes()` - Cycle through all programmed altitude codes

### Serial Monitor Output

The Serial Monitor displays:
- Current RPM reading
- Target altitude (calculated from RPM)
- Current display altitude
- Status messages when display updates

## Troubleshooting

1. **No RPM Reading**: Check tachometer signal connection and ensure it's a clean digital signal
2. **Incorrect Display**: Verify pin connections and check the altitude lookup table
3. **Erratic Behavior**: Add debouncing or filtering to the tachometer input if needed

## Based On

Sperry Component Maintenance Manual PN 7004577, Test Description for Test 7.16

## Notes

- The instrument expects pins to be grounded (LOW) to activate specific display segments
- Pins are set HIGH (not grounded) by default
- The lookup table currently includes basic altitude codes - expand as needed
- RPM calculation assumes 1 pulse per revolution - adjust for your tachometer
