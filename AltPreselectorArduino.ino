/*
 * Altitude Preselector Arduino Controller
 * 
 * This sketch reads a tachometer input and controls a Sperry instrument
 * by grounding specific pins (DD0-DD5) to display altitude values.
 * 
 * Based on Sperry Component Maintenance Manual PN 7004577
 * Test Description for Test 7.16
 */

// Pin definitions
const int TACH_INPUT_PIN = 2;        // Tachometer input (interrupt pin)
const int DD0_PIN = 3;               // Digital output DD0
const int DD1_PIN = 4;               // Digital output DD1  
const int DD2_PIN = 5;               // Digital output DD2
const int DD3_PIN = 6;               // Digital output DD3
const int DD4_PIN = 7;               // Digital output DD4 (10,000's)
const int DD5_PIN = 8;               // Digital output DD5 (1,000's)

// Tachometer variables
volatile unsigned long tachPulseCount = 0;
volatile unsigned long lastTachTime = 0;
unsigned long currentRPM = 0;
unsigned long lastRPMUpdate = 0;
const unsigned long RPM_UPDATE_INTERVAL = 1000; // Update RPM every 1 second

// Altitude mapping variables
int currentAltitude = 0;
int targetAltitude = 0;
const int MIN_ALTITUDE = 0;
const int MAX_ALTITUDE = 99999;  // Maximum 5-digit altitude

// Digital output pins array for easy control
const int outputPins[] = {DD0_PIN, DD1_PIN, DD2_PIN, DD3_PIN, DD4_PIN, DD5_PIN};
const int NUM_OUTPUT_PINS = 6;

// Altitude to digital code mapping based on the manual
// Each altitude value corresponds to a specific DD0-DD5 pattern
struct AltitudeCode {
  int altitude;
  bool dd0, dd1, dd2, dd3, dd4, dd5;
};

// Lookup table for altitude codes (based on manual data)
const AltitudeCode altitudeCodes[] = {
  {0,     false, false, false, false, false, false},  // 0
  {1000,  true,  false, false, true,  false, true},   // 1
  {2000,  false, true,  false, false, false, true},   // 2
  {3000,  true,  true,  false, true,  false, true},   // 3
  {4000,  false, false, true,  false, false, true},   // 4
  {5000,  true,  false, true,  true,  false, true},   // 5
  {6000,  false, true,  true,  false, false, true},   // 6
  {7000,  true,  true,  true,  true,  false, true},   // 7
  {8000,  false, false, false, true,  false, true},   // 8
  {9000,  true,  false, false, false, false, true},   // 9
  {10000, false, true,  false, true,  true,  false},  // 10 (10,000's)
  // Add more altitude codes as needed
};

const int NUM_ALTITUDE_CODES = sizeof(altitudeCodes) / sizeof(AltitudeCode);

void setup() {
  Serial.begin(9600);
  Serial.println("Altitude Preselector Controller Starting...");
  
  // Initialize tachometer input
  pinMode(TACH_INPUT_PIN, INPUT_PULLUP);
  attachInterrupt(digitalPinToInterrupt(TACH_INPUT_PIN), tachPulseISR, RISING);
  
  // Initialize output pins (set as outputs, initially HIGH - not grounded)
  for (int i = 0; i < NUM_OUTPUT_PINS; i++) {
    pinMode(outputPins[i], OUTPUT);
    digitalWrite(outputPins[i], HIGH);  // HIGH = not grounded
  }
  
  Serial.println("System initialized. Monitoring tachometer input...");
}

void loop() {
  // Update RPM calculation
  updateRPM();
  
  // Convert RPM to target altitude
  updateTargetAltitude();
  
  // Update display if altitude changed
  if (targetAltitude != currentAltitude) {
    currentAltitude = targetAltitude;
    updateDisplay();
  }
  
  // Print status every 2 seconds
  static unsigned long lastStatusPrint = 0;
  if (millis() - lastStatusPrint > 2000) {
    printStatus();
    lastStatusPrint = millis();
  }
  
  delay(50);  // Small delay for stability
}

// Interrupt service routine for tachometer pulses
void tachPulseISR() {
  tachPulseCount++;
  lastTachTime = millis();
}

// Calculate RPM from pulse count
void updateRPM() {
  if (millis() - lastRPMUpdate >= RPM_UPDATE_INTERVAL) {
    // Calculate RPM (assuming 1 pulse per revolution)
    // Adjust the multiplier based on your tachometer's pulses per revolution
    currentRPM = (tachPulseCount * 60000) / RPM_UPDATE_INTERVAL;
    
    tachPulseCount = 0;  // Reset pulse count
    lastRPMUpdate = millis();
  }
}

// Convert RPM to target altitude
void updateTargetAltitude() {
  // Simple linear mapping - adjust this function based on your requirements
  // This maps 0-3000 RPM to 0-30000 feet altitude
  targetAltitude = map(currentRPM, 0, 3000, MIN_ALTITUDE, 30000);
  
  // Constrain to valid range
  targetAltitude = constrain(targetAltitude, MIN_ALTITUDE, MAX_ALTITUDE);
  
  // Round to nearest 1000 feet for simplicity
  targetAltitude = (targetAltitude / 1000) * 1000;
}

// Update the display by grounding appropriate pins
void updateDisplay() {
  // First, set all pins HIGH (not grounded)
  for (int i = 0; i < NUM_OUTPUT_PINS; i++) {
    digitalWrite(outputPins[i], HIGH);
  }
  
  // Find the matching altitude code
  for (int i = 0; i < NUM_ALTITUDE_CODES; i++) {
    if (altitudeCodes[i].altitude == currentAltitude) {
      // Ground the pins that should be LOW according to the code
      digitalWrite(DD0_PIN, altitudeCodes[i].dd0 ? LOW : HIGH);
      digitalWrite(DD1_PIN, altitudeCodes[i].dd1 ? LOW : HIGH);
      digitalWrite(DD2_PIN, altitudeCodes[i].dd2 ? LOW : HIGH);
      digitalWrite(DD3_PIN, altitudeCodes[i].dd3 ? LOW : HIGH);
      digitalWrite(DD4_PIN, altitudeCodes[i].dd4 ? LOW : HIGH);
      digitalWrite(DD5_PIN, altitudeCodes[i].dd5 ? LOW : HIGH);
      
      Serial.print("Display updated to: ");
      Serial.print(currentAltitude);
      Serial.println(" feet");
      return;
    }
  }
  
  // If no exact match found, display blank (all pins HIGH)
  Serial.println("Altitude not in lookup table - displaying blank");
}

// Print current status to serial monitor
void printStatus() {
  Serial.print("RPM: ");
  Serial.print(currentRPM);
  Serial.print(" | Target Altitude: ");
  Serial.print(targetAltitude);
  Serial.print(" ft | Current Display: ");
  Serial.print(currentAltitude);
  Serial.println(" ft");
}

// Manual altitude setting function (for testing)
void setManualAltitude(int altitude) {
  targetAltitude = constrain(altitude, MIN_ALTITUDE, MAX_ALTITUDE);
  currentAltitude = targetAltitude;
  updateDisplay();
}

// Test function to cycle through all altitude codes
void testAllAltitudes() {
  Serial.println("Testing all altitude codes...");
  for (int i = 0; i < NUM_ALTITUDE_CODES; i++) {
    currentAltitude = altitudeCodes[i].altitude;
    updateDisplay();
    delay(2000);  // Display each altitude for 2 seconds
  }
  Serial.println("Test complete");
}
