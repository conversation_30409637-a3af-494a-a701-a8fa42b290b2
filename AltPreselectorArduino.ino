/*
 * Altitude Preselector Arduino Controller
 *
 * This sketch reads voltage spikes from a DC motor encoder on two analog inputs
 * and controls a Sperry instrument by grounding specific pins (DD0-DD5) to display altitude values.
 * Counts up/down by 100 starting from 000000 based on motor direction.
 *
 * Based on Sperry Component Maintenance Manual PN 7004577
 * Test Description for Test 7.16
 */

// Pin definitions
const int ENCODER_A_PIN = A0;        // Encoder channel A (analog input)
const int ENCODER_B_PIN = A1;        // Encoder channel B (analog input)
const int DD0_PIN = 3;               // Digital output DD0
const int DD1_PIN = 4;               // Digital output DD1
const int DD2_PIN = 5;               // Digital output DD2
const int DD3_PIN = 6;               // Digital output DD3
const int DD4_PIN = 7;               // Digital output DD4 (10,000's)
const int DD5_PIN = 8;               // Digital output DD5 (1,000's)

// Encoder variables
const int VOLTAGE_THRESHOLD = 512;   // Threshold for detecting voltage spike (adjust as needed)
const int DEBOUNCE_DELAY = 50;       // Debounce delay in milliseconds
int lastEncoderA = 0;
int lastEncoderB = 0;
unsigned long lastEncoderTime = 0;
bool encoderATriggered = false;
bool encoderBTriggered = false;

// Altitude counter variables
long currentAltitude = 0;            // Current altitude display (starts at 0)
const long MIN_ALTITUDE = 0;
const long MAX_ALTITUDE = 999900;    // Maximum altitude (999900 in increments of 100)
const int INCREMENT = 100;           // Altitude increment per encoder step

// Digital output pins array for easy control
const int outputPins[] = {DD0_PIN, DD1_PIN, DD2_PIN, DD3_PIN, DD4_PIN, DD5_PIN};
const int NUM_OUTPUT_PINS = 6;

// Function to convert altitude to BCD and set output pins
void setAltitudeDisplay(long altitude) {
  // Convert altitude to 6-digit BCD representation
  // Each digit is represented by 4 bits, but we're using the lookup table approach
  // from the manual for the specific digit patterns

  // Extract individual digits (assuming altitude is in hundreds, so 1000 = 10.00)
  int digit0 = (altitude / 1) % 10;        // Ones (always 0 for hundreds)
  int digit1 = (altitude / 10) % 10;       // Tens (always 0 for hundreds)
  int digit2 = (altitude / 100) % 10;      // Hundreds
  int digit3 = (altitude / 1000) % 10;     // Thousands
  int digit4 = (altitude / 10000) % 10;    // Ten thousands
  int digit5 = (altitude / 100000) % 10;   // Hundred thousands

  // For now, we'll use a simplified approach where we display the altitude
  // as a 6-digit number. You may need to adjust this based on the exact
  // display format required by your instrument.

  // Set all pins HIGH initially (not grounded)
  for (int i = 0; i < NUM_OUTPUT_PINS; i++) {
    digitalWrite(outputPins[i], HIGH);
  }

  // Simple binary representation of the altitude value
  // Ground pins based on binary representation of current altitude/100
  long displayValue = altitude / 100;  // Convert to display units

  // Use the lower 6 bits to control the 6 output pins
  for (int i = 0; i < NUM_OUTPUT_PINS; i++) {
    if (displayValue & (1L << i)) {
      digitalWrite(outputPins[i], LOW);  // Ground the pin
    }
  }
}

void setup() {
  Serial.begin(9600);
  Serial.println("Altitude Preselector Controller Starting...");

  // Initialize analog inputs for encoder
  pinMode(ENCODER_A_PIN, INPUT);
  pinMode(ENCODER_B_PIN, INPUT);

  // Initialize output pins (set as outputs, initially HIGH - not grounded)
  for (int i = 0; i < NUM_OUTPUT_PINS; i++) {
    pinMode(outputPins[i], OUTPUT);
    digitalWrite(outputPins[i], HIGH);  // HIGH = not grounded
  }

  // Initialize encoder readings
  lastEncoderA = analogRead(ENCODER_A_PIN);
  lastEncoderB = analogRead(ENCODER_B_PIN);

  // Set initial display to 000000
  setAltitudeDisplay(currentAltitude);

  Serial.println("System initialized. Monitoring encoder inputs...");
  Serial.print("Starting altitude: ");
  Serial.println(currentAltitude);
}

void loop() {
  // Check for encoder changes
  checkEncoder();

  // Print status every 2 seconds
  static unsigned long lastStatusPrint = 0;
  if (millis() - lastStatusPrint > 2000) {
    printStatus();
    lastStatusPrint = millis();
  }

  delay(10);  // Small delay for stability
}

// Check encoder inputs for voltage spikes and determine direction
void checkEncoder() {
  // Read current analog values
  int currentEncoderA = analogRead(ENCODER_A_PIN);
  int currentEncoderB = analogRead(ENCODER_B_PIN);

  // Check for voltage spike on channel A
  if (!encoderATriggered && currentEncoderA > VOLTAGE_THRESHOLD && lastEncoderA <= VOLTAGE_THRESHOLD) {
    encoderATriggered = true;
    lastEncoderTime = millis();

    // Check channel B to determine direction
    if (currentEncoderB > VOLTAGE_THRESHOLD) {
      // Both channels high - count up
      incrementAltitude();
    } else {
      // A high, B low - count down
      decrementAltitude();
    }
  }

  // Check for voltage spike on channel B
  if (!encoderBTriggered && currentEncoderB > VOLTAGE_THRESHOLD && lastEncoderB <= VOLTAGE_THRESHOLD) {
    encoderBTriggered = true;
    lastEncoderTime = millis();

    // Check channel A to determine direction
    if (currentEncoderA > VOLTAGE_THRESHOLD) {
      // Both channels high - count up
      incrementAltitude();
    } else {
      // B high, A low - count down
      decrementAltitude();
    }
  }

  // Reset triggers after debounce delay
  if (millis() - lastEncoderTime > DEBOUNCE_DELAY) {
    encoderATriggered = false;
    encoderBTriggered = false;
  }

  // Update last readings
  lastEncoderA = currentEncoderA;
  lastEncoderB = currentEncoderB;
}

// Increment altitude by 100
void incrementAltitude() {
  if (currentAltitude < MAX_ALTITUDE) {
    currentAltitude += INCREMENT;
    setAltitudeDisplay(currentAltitude);
    Serial.print("Altitude incremented to: ");
    Serial.println(currentAltitude);
  } else {
    Serial.println("Maximum altitude reached");
  }
}

// Decrement altitude by 100
void decrementAltitude() {
  if (currentAltitude > MIN_ALTITUDE) {
    currentAltitude -= INCREMENT;
    setAltitudeDisplay(currentAltitude);
    Serial.print("Altitude decremented to: ");
    Serial.println(currentAltitude);
  } else {
    Serial.println("Minimum altitude reached");
  }
}

// Print current status to serial monitor
void printStatus() {
  Serial.print("Current Altitude: ");
  Serial.print(currentAltitude);
  Serial.print(" | Encoder A: ");
  Serial.print(analogRead(ENCODER_A_PIN));
  Serial.print(" | Encoder B: ");
  Serial.println(analogRead(ENCODER_B_PIN));
}

// Manual altitude setting function (for testing)
void setManualAltitude(long altitude) {
  currentAltitude = constrain(altitude, MIN_ALTITUDE, MAX_ALTITUDE);
  // Round to nearest 100
  currentAltitude = (currentAltitude / INCREMENT) * INCREMENT;
  setAltitudeDisplay(currentAltitude);
  Serial.print("Manual altitude set to: ");
  Serial.println(currentAltitude);
}

// Test function to cycle through altitude values
void testAltitudeRange() {
  Serial.println("Testing altitude range...");
  for (long testAlt = 0; testAlt <= 1000; testAlt += 100) {
    currentAltitude = testAlt;
    setAltitudeDisplay(currentAltitude);
    Serial.print("Testing altitude: ");
    Serial.println(currentAltitude);
    delay(1000);  // Display each altitude for 1 second
  }
  currentAltitude = 0;  // Reset to 0
  setAltitudeDisplay(currentAltitude);
  Serial.println("Test complete - reset to 0");
}
